'use client'

import { useDownloader } from '@/hooks/download/downloader';
import DownloadCard from './download-card';
import { IconArrowRight } from '@tabler/icons-react';
import { ExtensionStatus } from '@/types/download';
import { useTranslations } from 'next-intl';

function App() {
  const {
    downloadData,
    downloadProgress,
    startDownloadWithData,
    saveToLocal,
    savedFiles,
    updateDownloadDataFilename,
    stopLiveRecording,
    extensionStatus
  } = useDownloader();
  const t = useTranslations('downloadComponents');

  return (
    < >
      {/* 插件检测中、未安装或没有下载数据 - 显示禁用状态的立即开始按钮 */}
      {(extensionStatus === ExtensionStatus.DETECTING ||
        extensionStatus === ExtensionStatus.NOT_INSTALLED ||
        (extensionStatus === ExtensionStatus.INSTALLED && !downloadData)) && (
          <button
            disabled={true}
            className="flex flex-row justify-center items-center px-6 py-3.5 gap-2 border-none rounded-lg cursor-pointer bg-blue-700 text-white h-[52px]"
          >
            <span className="font-inter font-medium text-base leading-6 text-white">
              {t('startNow')}
            </span>

            <IconArrowRight
              size={16}
              color="#FFFFFF"
            />
          </button>
        )}

      {/* 插件已安装且有下载数据 - 显示下载卡片 */}
      {extensionStatus === ExtensionStatus.INSTALLED && downloadData && (
        <>
          <DownloadCard
            downloadData={downloadData}
            downloadProgress={downloadProgress}
            startDownloadWithData={startDownloadWithData}
            saveToLocal={saveToLocal}
            savedFiles={savedFiles}
            updateDownloadDataFilename={updateDownloadDataFilename}
            stopLiveRecording={stopLiveRecording}
          />
        </>
      )}
    </>
  );
}

export default App;